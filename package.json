{"name": "nextjs-15-ecommerce", "version": "0.1.0", "private": true, "scripts": {"seed": "npx tsx ./src/lib/db/seed.ts", "dev": "next dev --turbopack", "email": "email dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.8.0", "@hookform/resolvers": "^4.1.3", "@paypal/react-paypal-js": "^8.8.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@react-email/components": "^0.0.34", "@stripe/react-stripe-js": "^3.5.1", "@stripe/stripe-js": "^5.10.0", "@types/bcrypt": "^5.0.2", "add": "^2.0.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dialog": "^0.3.1", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "i": "^0.3.7", "lucide-react": "^0.482.0", "mongodb": "^6.15.0", "mongoose": "^8.12.1", "next": "15.2.3", "next-auth": "^5.0.0-beta.25", "npm": "^11.2.0", "npx": "^10.2.2", "popover": "^2.4.1", "progress": "^2.0.3", "query-string": "^9.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-medium-image-zoom": "^5.2.14", "resend": "^4.2.0", "shadcn": "^2.4.0-canary.17", "stripe": "^17.5.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "textarea": "^0.3.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "react-email": "^3.0.7", "tailwindcss": "^4", "typescript": "^5"}}