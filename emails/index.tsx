import { SENDER_EMAIL, SENDER_NAME } from "@/lib/constant";
import { IOrder } from "@/lib/db/models/order.model";
// TODO: Uncomment when RESEND_API_KEY is available
// import { Resend } from 'resend';
import PurchaseReceiptEmail from "./purchase-receipt";

// TODO: Uncomment when RESEND_API_KEY is available
// const resend = new Resend(process.env.RESEND_API_KEY  as string)

export const sendPurchaseReceipt = async({order}:{order:IOrder}) => {
    // TODO: Uncomment when RESEND_API_KEY is available
    // await resend.emails.send({
    //     from: `${SENDER_NAME} <${SENDER_EMAIL}>`,
    //     to: (order.user as {email: string}).email,
    //     subject: `Order Confirmation`,
    //     react: <PurchaseReceiptEmail order={order} />
    // })

    // Temporary: Log email details instead of sending
    console.log('Email would be sent to:', (order.user as {email: string}).email);
    console.log('Order ID:', order._id);
    console.log('Total Price:', order.totalPrice);
}